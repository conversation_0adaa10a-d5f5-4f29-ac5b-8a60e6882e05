-- Client-side bridge functions for ESX and QBCore compatibility
-- Optimized for high player count servers

Bridge = {}

-- Performance optimizations
local FrameworkObj = nil
local NotificationCache = {}

-- QBCore initialization - optimized with cached framework object
function InitializeQBCore()
    if not FrameworkObj or not FrameworkObj.Functions then
        print('[GM_Airdrop] QBCore object not properly initialized')
        return
    end

    -- Get player data
    Bridge.GetPlayerData = function()
        return FrameworkObj.Functions.GetPlayerData()
    end

    -- Check if player is loaded
    Bridge.IsPlayerLoaded = function()
        local playerData = Bridge.GetPlayerData()
        return playerData and playerData.citizenid ~= nil
    end
end

-- ESX initialization - optimized with cached framework object
function InitializeESX()
    if not FrameworkObj or not FrameworkObj.GetPlayerData then
        print('[GM_Airdrop] ESX object not properly initialized')
        return
    end

    -- Get player data
    Bridge.GetPlayerData = function()
        return FrameworkObj.GetPlayerData()
    end

    -- Check if player is loaded
    Bridge.IsPlayerLoaded = function()
        local playerData = Bridge.GetPlayerData()
        return playerData and playerData.identifier ~= nil
    end
end

-- Direct initialization based on config - no threads needed
local function InitializeBridge()
    if Config.Framework == 'qb' or (Config.Framework == 'auto' and GetResourceState('qb-core') == 'started') then
        -- QBCore initialization
        if GetResourceState('qb-core') == 'started' then
            FrameworkObj = exports['qb-core']:GetCoreObject()
            InitializeQBCore()
            print('[GM_Airdrop] QBCore client bridge initialized')
        else
            print('[GM_Airdrop] QBCore not found!')
        end
    elseif Config.Framework == 'esx' or (Config.Framework == 'auto' and GetResourceState('es_extended') == 'started') then
        -- ESX initialization
        if GetResourceState('es_extended') == 'started' then
            FrameworkObj = exports['es_extended']:getSharedObject()
            if not FrameworkObj then
                TriggerEvent('esx:getSharedObject', function(obj) FrameworkObj = obj end)
            end
            InitializeESX()
            print('[GM_Airdrop] ESX client bridge initialized')
        else
            print('[GM_Airdrop] ESX not found!')
        end
    else
        print('[GM_Airdrop] No supported framework detected!')
    end
end

-- Initialize immediately when the script loads
InitializeBridge()

-- Client-side wrapper for the shared notification function
function ShowNotificationClient(message, type, duration)
    ShowNotification(nil, message, type, duration) -- nil source for client-side
end

-- Export functions for easy access
exports('ShowNotification', ShowNotificationClient)
exports('GetPlayerData', function() return Bridge.GetPlayerData and Bridge.GetPlayerData() or nil end)
exports('IsPlayerLoaded', function() return Bridge.IsPlayerLoaded and Bridge.IsPlayerLoaded() or false end)
