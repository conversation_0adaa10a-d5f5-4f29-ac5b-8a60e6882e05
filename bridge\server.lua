-- Server-side bridge functions for ESX and QBCore compatibility
-- Optimized for high player count servers (200+ players)

Bridge = {}

-- Performance optimizations
local FrameworkObj = nil
local PlayerCache = {} -- Cache player objects to reduce framework calls
local CacheTimeout = 5000 -- Cache player objects for 5 seconds
local LastCacheClean = 0

-- Clean expired cache entries periodically
local function CleanPlayerCache()
    local currentTime = GetGameTimer()
    if currentTime - LastCacheClean > 30000 then -- Clean every 30 seconds
        local expiredTime = currentTime - CacheTimeout
        for source, data in pairs(PlayerCache) do
            if data.timestamp < expiredTime then
                PlayerCache[source] = nil
            end
        end
        LastCacheClean = currentTime
    end
end

-- QBCore initialization - optimized with caching
function InitializeQBCore()
    if not FrameworkObj or not FrameworkObj.Functions then
        print('[GM_Airdrop] QBCore object not properly initialized')
        return
    end

    -- Optimized get player function with caching
    Bridge.GetPlayer = function(source)
        CleanPlayerCache() -- Clean expired entries

        local currentTime = GetGameTimer()
        local cached = PlayerCache[source]

        if cached and (currentTime - cached.timestamp) < CacheTimeout then
            return cached.player
        end

        local player = FrameworkObj.Functions.GetPlayer(source)
        if player then
            PlayerCache[source] = {
                player = player,
                timestamp = currentTime
            }
        end
        return player
    end

    -- Add item to player
    Bridge.AddItem = function(source, item, amount, metadata)
        if IsOxInventoryEnabled() then
            return exports.ox_inventory:AddItem(source, item, amount, metadata)
        else
            local Player = Bridge.GetPlayer(source)
            if Player then
                return Player.Functions.AddItem(item, amount, false, metadata)
            end
        end
        return false
    end

    -- Remove item from player
    Bridge.RemoveItem = function(source, item, amount, metadata, slot)
        if IsOxInventoryEnabled() then
            return exports.ox_inventory:RemoveItem(source, item, amount, metadata, slot, false)
        else
            local Player = Bridge.GetPlayer(source)
            if Player then
                return Player.Functions.RemoveItem(item, amount, slot)
            end
        end
        return false
    end

    -- Create useable item
    Bridge.CreateUseableItem = function(item, callback)
        FrameworkObj.Functions.CreateUseableItem(item, callback)
    end

    -- Get item from inventory
    Bridge.GetItem = function(source, item, metadata, slot)
        if IsOxInventoryEnabled() then
            return exports.ox_inventory:GetItem(source, item, metadata, slot)
        else
            local Player = Bridge.GetPlayer(source)
            if Player then
                return Player.Functions.GetItemByName(item)
            end
        end
        return nil
    end

end

-- ESX initialization - optimized with caching
function InitializeESX()
    if not FrameworkObj or not FrameworkObj.GetPlayerFromId then
        print('[GM_Airdrop] ESX object not properly initialized')
        return
    end

    -- Optimized get player function with caching
    Bridge.GetPlayer = function(source)
        CleanPlayerCache() -- Clean expired entries

        local currentTime = GetGameTimer()
        local cached = PlayerCache[source]

        if cached and (currentTime - cached.timestamp) < CacheTimeout then
            return cached.player
        end

        local player = FrameworkObj.GetPlayerFromId(source)
        if player then
            PlayerCache[source] = {
                player = player,
                timestamp = currentTime
            }
        end
        return player
    end

    -- Add item to player
    Bridge.AddItem = function(source, item, amount, metadata)
        if IsOxInventoryEnabled() then
            return exports.ox_inventory:AddItem(source, item, amount, metadata)
        else
            local xPlayer = Bridge.GetPlayer(source)
            if xPlayer then
                xPlayer.addInventoryItem(item, amount)
                return true
            end
        end
        return false
    end

    -- Remove item from player
    Bridge.RemoveItem = function(source, item, amount, metadata, slot)
        if IsOxInventoryEnabled() then
            return exports.ox_inventory:RemoveItem(source, item, amount, metadata, slot, false)
        else
            local xPlayer = Bridge.GetPlayer(source)
            if xPlayer then
                xPlayer.removeInventoryItem(item, amount)
                return true
            end
        end
        return false
    end

    -- Create useable item
    Bridge.CreateUseableItem = function(item, callback)
        FrameworkObj.RegisterUsableItem(item, callback)
    end

    -- Get item from inventory
    Bridge.GetItem = function(source, item, metadata, slot)
        if IsOxInventoryEnabled() then
            return exports.ox_inventory:GetItem(source, item, metadata, slot)
        else
            local xPlayer = Bridge.GetPlayer(source)
            if xPlayer then
                return xPlayer.getInventoryItem(item)
            end
        end
        return nil
    end

end

-- Direct initialization based on config - no threads needed
local function InitializeBridge()
    if Config.Framework == 'qb' or (Config.Framework == 'auto' and GetResourceState('qb-core') == 'started') then
        -- QBCore initialization
        if GetResourceState('qb-core') == 'started' then
            FrameworkObj = exports['qb-core']:GetCoreObject()
            InitializeQBCore()
            print('[GM_Airdrop] QBCore server bridge initialized')
        else
            print('[GM_Airdrop] QBCore not found!')
        end
    elseif Config.Framework == 'esx' or (Config.Framework == 'auto' and GetResourceState('es_extended') == 'started') then
        -- ESX initialization
        if GetResourceState('es_extended') == 'started' then
            FrameworkObj = exports['es_extended']:getSharedObject()
            if not FrameworkObj then
                TriggerEvent('esx:getSharedObject', function(obj) FrameworkObj = obj end)
            end
            InitializeESX()
            print('[GM_Airdrop] ESX server bridge initialized')
        else
            print('[GM_Airdrop] ESX not found!')
        end
    else
        print('[GM_Airdrop] No supported framework detected!')
    end
end

-- Initialize immediately when the script loads
InitializeBridge()

-- Universal functions with fallbacks
function GetPlayer(source)
    return Bridge.GetPlayer and Bridge.GetPlayer(source) or nil
end

function AddItem(source, item, amount, metadata)
    return Bridge.AddItem and Bridge.AddItem(source, item, amount, metadata) or false
end

function RemoveItem(source, item, amount, metadata, slot)
    return Bridge.RemoveItem and Bridge.RemoveItem(source, item, amount, metadata, slot) or false
end

function CreateUseableItem(item, callback)
    if Bridge.CreateUseableItem then
        Bridge.CreateUseableItem(item, callback)
    end
end

function GetItem(source, item, metadata, slot)
    return Bridge.GetItem and Bridge.GetItem(source, item, metadata, slot) or nil
end

function NotifyPlayer(source, message, type, duration)
    ShowNotification(source, message, type, duration)
end

-- Clean up player cache when they disconnect
AddEventHandler('playerDropped', function()
    local source = source
    if PlayerCache[source] then
        PlayerCache[source] = nil
    end
end)

-- Export functions for easy access
exports('GetPlayer', GetPlayer)
exports('AddItem', AddItem)
exports('RemoveItem', RemoveItem)
exports('CreateUseableItem', CreateUseableItem)
exports('GetItem', GetItem)
exports('NotifyPlayer', NotifyPlayer)
