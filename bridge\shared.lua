-- Bridge System for ESX and QBCore compatibility
-- Simplified config-based approach - no threads needed

-- Performance optimizations
local FrameworkCache = {
    oxInventoryEnabled = nil,
    lastInventoryCheck = 0,
    inventoryCheckInterval = 30000 -- Check every 30 seconds instead of every call
}

-- Initialize ox_inventory cache immediately
FrameworkCache.oxInventoryEnabled = GetResourceState('ox_inventory') == 'started'
FrameworkCache.lastInventoryCheck = GetGameTimer()

-- Optimized ox_inventory check with caching
function IsOxInventoryEnabled()
    local currentTime = GetGameTimer()
    if currentTime - FrameworkCache.lastInventoryCheck > FrameworkCache.inventoryCheckInterval then
        FrameworkCache.oxInventoryEnabled = GetResourceState('ox_inventory') == 'started'
        FrameworkCache.lastInventoryCheck = currentTime
    end
    return FrameworkCache.oxInventoryEnabled
end

-- Universal notification function - simplified single config
function ShowNotification(source, message, type, duration)
    duration = duration or 5000

    -- Determine if this is client or server side
    if IsDuplicityVersion() then
        -- Server-side notification
        if Config.NotificationSystem == 'framework' then
            -- Use framework-specific notification events
            if Config.Framework == 'qb' or (Config.Framework == 'auto' and GetResourceState('qb-core') == 'started') then
                TriggerClientEvent('QBCore:Notify', source, message, type, duration)
            elseif Config.Framework == 'esx' or (Config.Framework == 'auto' and GetResourceState('es_extended') == 'started') then
                TriggerClientEvent('esx:showNotification', source, message, type, duration)
            else
                -- Framework fallback to ox_lib
                TriggerClientEvent('ox_lib:notify', source, {
                    description = message,
                    type = type or 'inform'
                })
            end
        elseif Config.NotificationSystem == 'ox_lib' then
            -- ox_lib notifications
            TriggerClientEvent('ox_lib:notify', source, {
                description = message,
                type = type or 'inform',
                duration = duration
            })
        elseif Config.NotificationSystem == 'mythic' then
            -- Mythic notify support
            TriggerClientEvent('mythic_notify:client:SendAlert', source, {
                type = type or 'inform',
                text = message,
                length = duration
            })
        elseif Config.NotificationSystem == 'okoknotify' then
            -- okokNotify support
            TriggerClientEvent('okokNotify:Alert', source, 'GM Airdrop', message, duration, type or 'info')
        end
    else
        -- Client-side notification
        if Config.NotificationSystem == 'framework' then
            -- Use framework-specific notifications
            if Config.Framework == 'qb' or (Config.Framework == 'auto' and GetResourceState('qb-core') == 'started') then
                local QBCore = exports['qb-core']:GetCoreObject()
                if QBCore and QBCore.Functions then
                    QBCore.Functions.Notify(message, type, duration)
                else
                    ShowNotificationFallback(message, type, duration)
                end
            elseif Config.Framework == 'esx' or (Config.Framework == 'auto' and GetResourceState('es_extended') == 'started') then
                local ESX = exports['es_extended']:getSharedObject()
                if ESX and ESX.ShowNotification then
                    local esxType = ConvertNotificationType(type)
                    ESX.ShowNotification(message, esxType, duration)
                else
                    ShowNotificationFallback(message, type, duration)
                end
            else
                ShowNotificationFallback(message, type, duration)
            end
        elseif Config.NotificationSystem == 'ox_lib' then
            -- ox_lib notifications
            lib.notify({
                description = message,
                type = type or 'inform',
                duration = duration
            })
        elseif Config.NotificationSystem == 'native' then
            -- Native GTA notifications
            ShowNotificationFallback(message, type, duration)
        elseif Config.NotificationSystem == 'mythic' then
            -- Mythic notify support
            if GetResourceState('mythic_notify') == 'started' then
                exports['mythic_notify']:DoHudText(type or 'inform', message, duration)
            else
                ShowNotificationFallback(message, type, duration)
            end
        elseif Config.NotificationSystem == 'okoknotify' then
            -- okokNotify support
            if GetResourceState('okokNotify') == 'started' then
                exports['okokNotify']:Alert('GM Airdrop', message, duration, type or 'info')
            else
                ShowNotificationFallback(message, type, duration)
            end
        end
    end
end

-- Helper function to convert notification types
function ConvertNotificationType(type)
    local typeMap = {
        success = 'success',
        error = 'error',
        primary = 'info',
        info = 'info',
        inform = 'info'
    }
    return typeMap[type] or 'info'
end

-- Fallback notification function
function ShowNotificationFallback(message, type, duration)
    SetNotificationTextEntry('STRING')
    AddTextComponentString(message)
    DrawNotification(false, true)
end
